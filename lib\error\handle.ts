import { toast } from 'sonner';
import { parseError } from './parse';

export const handleError = (title: string, error: unknown) => {
  const parsedError = parseError(error);

  toast.error(title, {
    description: parsedError.message,
    action:
      parsedError.type !== 'unknown_error'
        ? {
            label: 'Details',
            onClick: () => {
              try {
                // Safely log error details without circular references
                const safeErrorDetails = {
                  message: parsedError.message,
                  type: parsedError.type,
                  status: parsedError.status,
                  details: parsedError.details
                    ? JSON.stringify(parsedError.details, null, 2)
                    : 'No details available',
                };
                console.error('Error details:', safeErrorDetails);
              } catch (logError) {
                console.error('Error details (fallback):', {
                  message: parsedError.message,
                  type: parsedError.type,
                  status: parsedError.status,
                });
              }
            },
          }
        : undefined,
  });
};
