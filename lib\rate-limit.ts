import { env } from '@/lib/env';
import { Ratelimit, type RatelimitConfig } from '@upstash/ratelimit';
import { Redis } from '@upstash/redis';

// Check if we're in development with mock Redis values
const isDevelopmentWithMockRedis =
  process.env.NODE_ENV === 'development' &&
  (env.UPSTASH_REDIS_REST_URL?.includes('dummy') ||
    env.UPSTASH_REDIS_REST_TOKEN?.includes('dummy'));

// Create a mock rate limiter for development
const createMockRateLimiter = () => ({
  limit: async (identifier: string) => {
    console.log(
      `[DEV] Mock rate limiter - allowing request for: ${identifier}`
    );
    return {
      success: true,
      limit: 10,
      remaining: 9,
      reset: Date.now() + 10000,
    };
  },
});

export const redis = isDevelopmentWithMockRedis
  ? null
  : new Redis({
      url: env.UPSTASH_REDIS_REST_URL,
      token: env.UPSTASH_REDIS_REST_TOKEN,
    });

export const createRateLimiter = (props: Omit<RatelimitConfig, 'redis'>) => {
  if (isDevelopmentWithMockRedis) {
    console.log('[DEV] Using mock rate limiter - Redis not configured');
    return createMockRateLimiter();
  }

  return new Ratelimit({
    redis: redis!,
    limiter: props.limiter ?? Ratelimit.slidingWindow(10, '10 s'),
    prefix: props.prefix ?? 'next-forge',
  });
};

export const { slidingWindow } = Ratelimit;
