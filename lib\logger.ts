export interface LogContext {
  userId?: string;
  modelId?: string;
  prompt?: string;
  requestId?: string;
  timestamp?: string;
  [key: string]: unknown;
}

export interface LogEntry {
  level: 'debug' | 'info' | 'warn' | 'error';
  message: string;
  context?: LogContext;
  error?: unknown;
  stack?: string;
}

class Logger {
  private isDevelopment = process.env.NODE_ENV === 'development';

  private formatMessage(entry: LogEntry): string {
    const timestamp = new Date().toISOString();
    const level = entry.level.toUpperCase().padEnd(5);
    const contextStr = entry.context ? ` | Context: ${JSON.stringify(entry.context, null, 2)}` : '';
    const errorStr = entry.error ? ` | Error: ${JSON.stringify(entry.error, null, 2)}` : '';
    const stackStr = entry.stack ? ` | Stack: ${entry.stack}` : '';
    
    return `[${timestamp}] ${level} | ${entry.message}${contextStr}${errorStr}${stackStr}`;
  }

  private log(entry: LogEntry): void {
    const formattedMessage = this.formatMessage(entry);
    
    switch (entry.level) {
      case 'debug':
        if (this.isDevelopment) {
          console.debug(formattedMessage);
        }
        break;
      case 'info':
        console.info(formattedMessage);
        break;
      case 'warn':
        console.warn(formattedMessage);
        break;
      case 'error':
        console.error(formattedMessage);
        break;
    }
  }

  debug(message: string, context?: LogContext): void {
    this.log({ level: 'debug', message, context });
  }

  info(message: string, context?: LogContext): void {
    this.log({ level: 'info', message, context });
  }

  warn(message: string, context?: LogContext, error?: unknown): void {
    this.log({ level: 'warn', message, context, error });
  }

  error(message: string, error?: unknown, context?: LogContext): void {
    const stack = error instanceof Error ? error.stack : undefined;
    this.log({ level: 'error', message, context, error, stack });
  }

  // Specific methods for AI API debugging
  apiRequest(message: string, context: LogContext): void {
    this.debug(`[API REQUEST] ${message}`, context);
  }

  apiResponse(message: string, context: LogContext): void {
    this.debug(`[API RESPONSE] ${message}`, context);
  }

  apiError(message: string, error: unknown, context: LogContext): void {
    this.error(`[API ERROR] ${message}`, error, context);
  }

  modelValidation(message: string, context: LogContext): void {
    this.debug(`[MODEL VALIDATION] ${message}`, context);
  }

  authValidation(message: string, context: LogContext): void {
    this.debug(`[AUTH VALIDATION] ${message}`, context);
  }

  rateLimit(message: string, context: LogContext): void {
    this.warn(`[RATE LIMIT] ${message}`, context);
  }
}

export const logger = new Logger();
