#!/usr/bin/env node

/**
 * Test script for the enhanced AI Generate API
 * This script tests various error scenarios and debugging features
 */

const API_BASE_URL = 'http://localhost:3001';

// Test cases for different error scenarios
const testCases = [
  {
    name: 'Missing prompt',
    data: { modelId: 'openai-gpt-4o' },
    expectedStatus: 400,
    description: 'Should return 400 for missing prompt',
  },
  {
    name: 'Empty prompt',
    data: { prompt: '', modelId: 'openai-gpt-4o' },
    expectedStatus: 400,
    description: 'Should return 400 for empty prompt',
  },
  {
    name: 'Invalid prompt type',
    data: { prompt: 123, modelId: 'openai-gpt-4o' },
    expectedStatus: 400,
    description: 'Should return 400 for non-string prompt',
  },
  {
    name: 'Missing model ID',
    data: { prompt: 'Test prompt' },
    expectedStatus: 400,
    description: 'Should return 400 for missing model ID',
  },
  {
    name: 'Invalid model ID',
    data: { prompt: 'Test prompt', modelId: 'invalid-model' },
    expectedStatus: 400,
    description: 'Should return 400 for invalid model ID',
  },
  {
    name: 'Very long prompt',
    data: {
      prompt: 'A'.repeat(15000),
      modelId: 'openai-gpt-4o',
    },
    expectedStatus: 400,
    description: 'Should return 400 for prompt too long',
  },
  {
    name: 'Valid request',
    data: {
      prompt: 'Write a short story about a robot learning to paint.',
      modelId: 'openai-gpt-4o',
    },
    expectedStatus: [200, 401], // 200 if API keys are configured, 401 if not
    description: 'Should handle valid request appropriately',
  },
];

async function testAPI() {
  console.log('🧪 Testing Enhanced AI Generate API\n');
  console.log('='.repeat(60));

  let passedTests = 0;
  const totalTests = testCases.length;

  for (const testCase of testCases) {
    console.log(`\n📋 Test: ${testCase.name}`);
    console.log(`📝 Description: ${testCase.description}`);

    try {
      const response = await fetch(`${API_BASE_URL}/api/ai/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testCase.data),
      });

      const responseText = await response.text();
      let responseData;

      try {
        responseData = JSON.parse(responseText);
      } catch {
        responseData = { message: responseText };
      }

      console.log(`📊 Status: ${response.status}`);
      console.log(`📄 Response:`, JSON.stringify(responseData, null, 2));

      // Check if status matches expected
      const expectedStatuses = Array.isArray(testCase.expectedStatus)
        ? testCase.expectedStatus
        : [testCase.expectedStatus];

      if (expectedStatuses.includes(response.status)) {
        console.log('✅ PASSED');
        passedTests++;
      } else {
        console.log(
          `❌ FAILED - Expected status ${testCase.expectedStatus}, got ${response.status}`
        );
      }

      // Check for enhanced error information
      if (response.status >= 400 && responseData.requestId) {
        console.log(`🔍 Request ID found: ${responseData.requestId}`);
      }

      if (responseData.type) {
        console.log(`🏷️  Error type: ${responseData.type}`);
      }
    } catch (error) {
      console.log(`❌ FAILED - Network error: ${error.message}`);
    }

    console.log('-'.repeat(40));
  }

  console.log(`\n📈 Test Results: ${passedTests}/${totalTests} tests passed`);

  if (passedTests === totalTests) {
    console.log('🎉 All tests passed!');
  } else {
    console.log('⚠️  Some tests failed. Check the API implementation.');
  }

  console.log('\n💡 Tips for debugging:');
  console.log('- Check the server logs for detailed error information');
  console.log('- Look for request IDs in error responses');
  console.log('- Verify API keys are properly configured');
  console.log('- Check that the development server is running on port 3000');
}

// Run the tests
if (require.main === module) {
  testAPI().catch(console.error);
}

module.exports = { testAPI };
