export interface ParsedError {
  message: string;
  status: number;
  type: string;
  details?: Record<string, unknown>;
  stack?: string;
}

export const parseError = (error: unknown): ParsedError => {
  // Handle string errors
  if (typeof error === 'string') {
    return {
      message: error,
      status: 500,
      type: 'string_error',
    };
  }

  // Handle Error objects
  if (error instanceof Error) {
    const parsedError: ParsedError = {
      message: error.message,
      status: 500,
      type: error.constructor.name,
      stack: error.stack,
    };

    // Handle specific AI SDK errors
    if (error.message.includes('API key')) {
      parsedError.status = 401;
      parsedError.type = 'authentication_error';
      parsedError.message =
        'Invalid or missing API key. Please check your API key configuration.';
    } else if (
      error.message.includes('rate limit') ||
      error.message.includes('quota')
    ) {
      parsedError.status = 429;
      parsedError.type = 'rate_limit_error';
      parsedError.message = 'Rate limit exceeded. Please try again later.';
    } else if (
      error.message.includes('model') &&
      error.message.includes('not found')
    ) {
      parsedError.status = 400;
      parsedError.type = 'model_error';
      parsedError.message =
        'The specified model is not available or not found.';
    } else if (error.message.includes('timeout')) {
      parsedError.status = 408;
      parsedError.type = 'timeout_error';
      parsedError.message = 'Request timed out. Please try again.';
    } else if (
      error.message.includes('network') ||
      error.message.includes('connection')
    ) {
      parsedError.status = 503;
      parsedError.type = 'network_error';
      parsedError.message =
        'Network connection error. Please check your internet connection.';
    }

    return parsedError;
  }

  // Handle objects with error information
  if (typeof error === 'object' && error !== null) {
    const errorObj = error as Record<string, unknown>;

    return {
      message: String(
        errorObj.message || errorObj.error || 'Unknown error occurred'
      ),
      status: Number(errorObj.status || errorObj.statusCode || 500),
      type: String(errorObj.type || errorObj.name || 'object_error'),
      details: errorObj,
    };
  }

  // Fallback for unknown error types
  return {
    message: 'An unknown error occurred',
    status: 500,
    type: 'unknown_error',
    details: { originalError: error },
  };
};
