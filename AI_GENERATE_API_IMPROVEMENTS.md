# AI Generate API - Enhanced Error Handling & Debugging

## Overview

This document outlines the comprehensive improvements made to the AI text generation API (`app/api/ai/generate/route.ts`) to replace generic "Error generating text" messages with detailed debugging information and proper error handling.

## Key Improvements

### 1. Enhanced Error Parsing (`lib/error/parse.ts`)

**Before:**
```typescript
export const parseError = (error: unknown) => {
  if (typeof error === 'string') return error;
  if (error instanceof Error) return error.message;
  return 'An error occurred';
};
```

**After:**
- Returns structured `ParsedError` object with `message`, `status`, `type`, `details`, and `stack`
- Classifies errors by type (authentication, rate limit, model, timeout, network)
- Provides appropriate HTTP status codes
- Includes detailed error context for debugging

### 2. Comprehensive Logging System (`lib/logger.ts`)

**New Features:**
- Structured logging with context (userId, modelId, requestId, etc.)
- Different log levels (debug, info, warn, error)
- Specialized methods for API operations:
  - `apiRequest()` - Log API call initiation
  - `apiResponse()` - Log successful API responses
  - `apiError()` - Log API failures with context
  - `modelValidation()` - Log model lookup and validation
  - `authValidation()` - Log authentication steps
  - `rateLimit()` - Log rate limiting events

### 3. Enhanced API Route (`app/api/ai/generate/route.ts`)

**New Debugging Features:**

#### Request Tracking
- Unique request ID for each API call
- Request timing and duration tracking
- User agent and timestamp logging

#### Environment Validation
- Checks for required API keys (OpenAI, Google, Groq)
- Logs missing API keys for debugging

#### Detailed Input Validation
- Validates prompt type, length, and content
- Validates model ID and checks against available models
- Provides specific error messages for each validation failure

#### Enhanced Error Responses
- Structured JSON error responses with:
  - Error message and type
  - Request ID for tracking
  - Development-only error details
  - Appropriate HTTP status codes

#### API Call Monitoring
- Logs before and after AI API calls
- Tracks token usage and costs
- Handles credit tracking failures gracefully
- Provides detailed error context for AI API failures

## Error Types and Status Codes

| Error Type | Status Code | Description |
|------------|-------------|-------------|
| `authentication_error` | 401 | Invalid or missing API key |
| `rate_limit_error` | 429 | Rate limit exceeded |
| `model_error` | 400 | Invalid or unavailable model |
| `timeout_error` | 408 | Request timeout |
| `network_error` | 503 | Network connection issues |
| `validation_error` | 400 | Invalid input parameters |

## Example Error Response

**Before:**
```
Error generating text
```

**After:**
```json
{
  "error": "Invalid model: invalid-model-id. Please check available models.",
  "type": "model_error",
  "requestId": "req_abc123",
  "details": {
    "modelId": "invalid-model-id",
    "availableModels": ["openai-gpt-4o", "openai-gpt-4o-mini", ...]
  }
}
```

## Debugging Workflow

### 1. Check Server Logs
Look for detailed logs with request IDs:
```
[2024-01-15T10:30:00.000Z] INFO  | AI Generate API request started | Context: {"requestId":"req_abc123","timestamp":"2024-01-15T10:30:00.000Z"}
[2024-01-15T10:30:00.100Z] DEBUG | [AUTH VALIDATION] User authenticated successfully | Context: {"requestId":"req_abc123","userId":"user_123"}
[2024-01-15T10:30:00.200Z] ERROR | [API ERROR] AI text generation failed | Error: {"message":"Invalid API key"} | Context: {"requestId":"req_abc123","modelId":"openai-gpt-4o"}
```

### 2. Use Request IDs
Each error response includes a unique request ID that can be used to trace the request through the logs.

### 3. Check Error Types
Error responses include a `type` field that categorizes the error, making it easier to identify the root cause.

## Testing

Use the provided test script to verify the enhanced error handling:

```bash
node test-ai-generate-api.js
```

This script tests various error scenarios including:
- Missing or invalid prompts
- Invalid model IDs
- Oversized prompts
- Valid requests (with and without API keys)

## Configuration Requirements

Ensure the following environment variables are set for full functionality:

```env
OPENAI_API_KEY=your_openai_key
GOOGLE_GENERATIVE_AI_API_KEY=your_google_key
GROQ_API_KEY=your_groq_key
```

## Benefits

1. **Faster Debugging**: Detailed error messages and request tracking
2. **Better User Experience**: Specific error messages instead of generic ones
3. **Operational Visibility**: Comprehensive logging for monitoring
4. **Error Classification**: Structured error types for automated handling
5. **Development Support**: Enhanced error details in development mode

## Next Steps

1. Run the test script to verify functionality
2. Check server logs during API calls to see detailed debugging information
3. Monitor error types and frequencies for operational insights
4. Consider adding metrics collection based on error types and request IDs
