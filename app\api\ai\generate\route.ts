import { getSubscribedUser } from '@/lib/auth';
import { env } from '@/lib/env';
import { parseError } from '@/lib/error/parse';
import { logger } from '@/lib/logger';
import { textModels } from '@/lib/models/text';
import { createRateLimiter, slidingWindow } from '@/lib/rate-limit';
import { trackCreditUsage } from '@/lib/stripe';
import { streamText } from 'ai';

export const maxDuration = 30;

const rateLimiter = createRateLimiter({
  limiter: slidingWindow(10, '1 m'),
  prefix: 'api-generate',
});

export const POST = async (req: Request) => {
  const requestId = crypto.randomUUID();
  const startTime = Date.now();

  logger.info('AI Generate API request started', {
    requestId,
    timestamp: new Date().toISOString(),
    userAgent: req.headers.get('user-agent'),
  });

  try {
    // Validate environment variables for AI providers
    logger.debug('Validating environment variables', { requestId });
    const missingKeys: string[] = [];
    if (!env.OPENAI_API_KEY) {
      missingKeys.push('OPENAI_API_KEY');
    }
    if (!env.GOOGLE_GENERATIVE_AI_API_KEY) {
      missingKeys.push('GOOGLE_GENERATIVE_AI_API_KEY');
    }
    if (!env.GROQ_API_KEY) {
      missingKeys.push('GROQ_API_KEY');
    }

    if (missingKeys.length > 0) {
      logger.error('Missing required API keys', new Error('Missing API keys'), {
        requestId,
        missingKeys,
      });
    }

    // Authentication
    logger.authValidation('Checking user authentication', { requestId });
    const user = await getSubscribedUser();

    if (!user) {
      logger.warn('Authentication failed - no user found', { requestId });
      return new Response('Authentication required', { status: 401 });
    }

    logger.authValidation('User authenticated successfully', {
      requestId,
      userId: user.id,
      userEmail: user.email,
    });

    // Apply rate limiting with error handling
    logger.rateLimit('Checking rate limits', { requestId, userId: user.id });

    let rateLimitResult: { success: boolean };
    try {
      rateLimitResult = await rateLimiter.limit(user.id);
    } catch (rateLimitError) {
      logger.warn('Rate limiting failed, proceeding without rate limit', {
        requestId,
        userId: user.id,
        error:
          rateLimitError instanceof Error
            ? rateLimitError.message
            : String(rateLimitError),
      });
      // In development, proceed without rate limiting if Redis fails
      if (process.env.NODE_ENV === 'development') {
        rateLimitResult = { success: true };
      } else {
        // In production, fail safely
        logger.error('Rate limiting system unavailable', rateLimitError, {
          requestId,
          userId: user.id,
        });
        return new Response('Service temporarily unavailable', { status: 503 });
      }
    }

    if (!rateLimitResult.success) {
      logger.rateLimit('Rate limit exceeded', { requestId, userId: user.id });
      return new Response('Too many requests', { status: 429 });
    }

    logger.rateLimit('Rate limit check passed', { requestId, userId: user.id });

    // Parse request body
    logger.debug('Parsing request body', { requestId });
    let requestBody: { prompt?: unknown; modelId?: unknown };
    try {
      requestBody = await req.json();
    } catch (error) {
      logger.error('Failed to parse request body', error, { requestId });
      return new Response('Invalid JSON in request body', { status: 400 });
    }

    const { prompt, modelId } = requestBody;
    logger.debug('Request body parsed', {
      requestId,
      promptLength: typeof prompt === 'string' ? prompt.length : undefined,
      modelId: String(modelId),
      hasPrompt: !!prompt,
    });

    // Validate prompt
    if (!prompt || typeof prompt !== 'string') {
      logger.warn('Invalid prompt provided', {
        requestId,
        promptType: typeof prompt,
        promptLength: typeof prompt === 'string' ? prompt.length : undefined,
      });
      return new Response('Prompt is required and must be a string', {
        status: 400,
      });
    }

    if (prompt.length > 10000) {
      logger.warn('Prompt too long', {
        requestId,
        promptLength: prompt.length,
      });
      return new Response('Prompt is too long (max 10,000 characters)', {
        status: 400,
      });
    }

    // Validate model ID
    if (typeof modelId !== 'string') {
      logger.warn('Invalid model ID type', {
        requestId,
        modelIdType: typeof modelId,
      });
      return new Response('Model must be a string', { status: 400 });
    }

    // Find and validate model
    logger.modelValidation('Looking up model', { requestId, modelId });
    const model = textModels
      .flatMap((m) => m.models)
      .find((m) => m.id === modelId);

    if (!model) {
      logger.error('Model not found', new Error('Invalid model'), {
        requestId,
        modelId,
        availableModels: textModels.flatMap((m) =>
          m.models.map((model) => model.id)
        ),
      });
      return new Response(
        `Invalid model: ${modelId}. Please check available models.`,
        {
          status: 400,
        }
      );
    }

    logger.modelValidation('Model found and validated', {
      requestId,
      modelId: model.id,
      modelLabel: model.label,
      modelProvider: model.model.modelId,
    });

    // Make AI API call with comprehensive error handling
    logger.apiRequest('Starting AI text generation', {
      requestId,
      userId: user.id,
      modelId: model.id,
      promptLength: prompt.length,
      modelProvider: model.model.modelId,
    });

    try {
      const result = streamText({
        model: model.model,
        system:
          "You are a helpful assistant that generates text based on the user's prompt.",
        prompt,
        onFinish: async ({ usage }) => {
          logger.info('AI generation completed', {
            requestId,
            userId: user.id,
            modelId: model.id,
            promptTokens: usage.promptTokens,
            completionTokens: usage.completionTokens || 0,
            totalTokens: usage.totalTokens,
          });

          try {
            const cost = model.getCost({
              input: usage.promptTokens,
              output: usage.completionTokens || 0,
            });

            await trackCreditUsage({
              action: 'generate',
              cost,
            });

            logger.debug('Credit usage tracked successfully', {
              requestId,
              userId: user.id,
            });
          } catch (creditError) {
            logger.error('Failed to track credit usage', creditError, {
              requestId,
              userId: user.id,
            });
            // Don't fail the request if credit tracking fails
          }
        },
      });

      logger.apiResponse('AI text generation stream created successfully', {
        requestId,
        userId: user.id,
        modelId: model.id,
      });

      return result.toDataStreamResponse();
    } catch (error) {
      logger.apiError('AI text generation failed', error, {
        requestId,
        userId: user.id,
        modelId: model.id,
        modelProvider: model.model.modelId,
        promptLength: prompt.length,
        errorType:
          error instanceof Error ? error.constructor.name : typeof error,
        errorMessage: error instanceof Error ? error.message : String(error),
      });

      const parsedError = parseError(error);

      return new Response(
        JSON.stringify({
          error: parsedError.message,
          type: parsedError.type,
          requestId,
          details:
            process.env.NODE_ENV === 'development'
              ? parsedError.details
              : undefined,
        }),
        {
          status: parsedError.status,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }
  } catch (error) {
    // Handle any unexpected errors at the top level
    logger.error('Unexpected error in AI generate API', error, {
      requestId,
      errorType: error instanceof Error ? error.constructor.name : typeof error,
      errorMessage: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    });

    const parsedError = parseError(error);

    return new Response(
      JSON.stringify({
        error: parsedError.message,
        type: parsedError.type,
        requestId,
        details:
          process.env.NODE_ENV === 'development'
            ? parsedError.details
            : undefined,
      }),
      {
        status: parsedError.status,
        headers: { 'Content-Type': 'application/json' },
      }
    );
  } finally {
    // Log request completion
    const duration = Date.now() - startTime;
    logger.info('AI Generate API request completed', {
      requestId,
      duration: `${duration}ms`,
    });
  }
};
